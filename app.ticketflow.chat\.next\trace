[{"traceId": "cc43856c08056924", "parentId": 1, "name": "generate-buildid", "id": 4, "timestamp": 473266905, "duration": 221, "tags": {}, "startTime": 1753034907457}, {"traceId": "cc43856c08056924", "parentId": 1, "name": "load-custom-routes", "id": 5, "timestamp": 473267295, "duration": 202, "tags": {}, "startTime": 1753034907457}, {"traceId": "cc43856c08056924", "parentId": 1, "name": "verify-and-lint", "id": 7, "timestamp": 473522951, "duration": 2320720, "tags": {}, "startTime": 1753034907713}, {"traceId": "cc43856c08056924", "parentId": 1, "name": "verify-typescript-setup", "id": 6, "timestamp": 473416831, "duration": 9407554, "tags": {}, "startTime": 1753034907607}, {"traceId": "cc43856c08056924", "parentId": 1, "name": "collect-pages", "id": 8, "timestamp": 482836441, "duration": 5873, "tags": {}, "startTime": 1753034917026}, {"traceId": "cc43856c08056924", "parentId": 1, "name": "create-pages-mapping", "id": 9, "timestamp": 482843177, "duration": 945, "tags": {}, "startTime": 1753034917033}, {"traceId": "cc43856c08056924", "parentId": 1, "name": "create-entrypoints", "id": 10, "timestamp": 482844158, "duration": 104992, "tags": {}, "startTime": 1753034917034}, {"traceId": "cc43856c08056924", "parentId": 1, "name": "public-dir-conflict-check", "id": 11, "timestamp": 482951953, "duration": 7677, "tags": {}, "startTime": 1753034917142}, {"traceId": "cc43856c08056924", "parentId": 1, "name": "generate-routes-manifest", "id": 12, "timestamp": 482959874, "duration": 2166, "tags": {}, "startTime": 1753034917150}, {"traceId": "cc43856c08056924", "parentId": 1, "name": "create-dist-dir", "id": 13, "timestamp": 482962055, "duration": 535, "tags": {}, "startTime": 1753034917152}, {"traceId": "cc43856c08056924", "parentId": 1, "name": "write-routes-manifest", "id": 14, "timestamp": 482972576, "duration": 521, "tags": {}, "startTime": 1753034917163}, {"traceId": "cc43856c08056924", "parentId": 1, "name": "generate-required-server-files", "id": 15, "timestamp": 482973119, "duration": 328, "tags": {}, "startTime": 1753034917163}, {"traceId": "cc43856c08056924", "parentId": 16, "name": "generate-webpack-config", "id": 17, "timestamp": 482973478, "duration": 811310, "tags": {}, "startTime": 1753034917163}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "next-trace-entrypoint-plugin", "id": 19, "timestamp": 483845668, "duration": 2046, "tags": {}, "startTime": 1753034918036}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 26, "timestamp": 483853463, "duration": 237578, "tags": {"request": "private-next-pages/api/hello.ts"}, "startTime": 1753034918043}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 22, "timestamp": 483853408, "duration": 510843, "tags": {"request": "next/dist/pages/_error"}, "startTime": 1753034918043}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 56, "timestamp": 483853763, "duration": 511339, "tags": {"request": "private-next-pages/group/[id].tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 73, "name": "read-resource", "id": 74, "timestamp": 484519251, "duration": 66, "tags": {}, "startTime": 1753034918709}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "build-module-json", "id": 73, "timestamp": 484517595, "duration": 3299, "tags": {"name": "C:\\OSPanel\\home\\app.ticketflow.chat\\locales\\en\\translation.json"}, "startTime": 1753034918708}, {"traceId": "cc43856c08056924", "parentId": 75, "name": "read-resource", "id": 76, "timestamp": 484554490, "duration": 2872, "tags": {}, "startTime": 1753034918744}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "build-module-json", "id": 75, "timestamp": 484554398, "duration": 3452, "tags": {"name": "C:\\OSPanel\\home\\app.ticketflow.chat\\locales\\pt-BR\\translation.json"}, "startTime": 1753034918744}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 55, "timestamp": 483853761, "duration": 711145, "tags": {"request": "private-next-pages/careers/[id].tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 62, "timestamp": 483853776, "duration": 711148, "tags": {"request": "private-next-pages/referral-terms.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 58, "timestamp": 483853767, "duration": 711169, "tags": {"request": "private-next-pages/privacy.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 69, "timestamp": 483853795, "duration": 711145, "tags": {"request": "private-next-pages/terms.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 23, "timestamp": 483853429, "duration": 711807, "tags": {"request": "private-next-pages/_document.tsx"}, "startTime": 1753034918043}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 45, "timestamp": 483853736, "duration": 715184, "tags": {"request": "private-next-pages/settings/notification.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 46, "timestamp": 483853738, "duration": 719281, "tags": {"request": "private-next-pages/shop-category/index.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 52, "timestamp": 483853753, "duration": 719275, "tags": {"request": "private-next-pages/blog/index.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 53, "timestamp": 483853755, "duration": 719276, "tags": {"request": "private-next-pages/blog/[id].tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 78, "name": "next-swc-transform", "id": 79, "timestamp": 484558469, "duration": 66576, "tags": {}, "startTime": 1753034918748}, {"traceId": "cc43856c08056924", "parentId": 77, "name": "next-swc-loader", "id": 78, "timestamp": 484557943, "duration": 67113, "tags": {}, "startTime": 1753034918748}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "build-module-tsx", "id": 77, "timestamp": 484554843, "duration": 92943, "tags": {"name": "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\addressModal.tsx"}, "startTime": 1753034918745}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 28, "timestamp": 483853487, "duration": 807356, "tags": {"request": "private-next-pages/brands.tsx"}, "startTime": 1753034918043}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 54, "timestamp": 483853758, "duration": 839218, "tags": {"request": "private-next-pages/about.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 57, "timestamp": 483853765, "duration": 839225, "tags": {"request": "private-next-pages/deliver.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 24, "timestamp": 483853443, "duration": 863150, "tags": {"request": "private-next-pages/404.tsx"}, "startTime": 1753034918043}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 70, "timestamp": 483853797, "duration": 883049, "tags": {"request": "private-next-pages/welcome.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 27, "timestamp": 483853476, "duration": 941432, "tags": {"request": "private-next-pages/be-seller.tsx"}, "startTime": 1753034918043}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 38, "timestamp": 483853710, "duration": 941217, "tags": {"request": "private-next-pages/profile.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 81, "name": "read-resource", "id": 82, "timestamp": 484794867, "duration": 71, "tags": {}, "startTime": 1753034918985}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 35, "timestamp": 483853700, "duration": 1128477, "tags": {"request": "private-next-pages/parcel-checkout.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 83, "name": "next-swc-transform", "id": 84, "timestamp": 484971874, "duration": 52885, "tags": {}, "startTime": 1753034919162}, {"traceId": "cc43856c08056924", "parentId": 80, "name": "next-swc-loader", "id": 83, "timestamp": 484971737, "duration": 53031, "tags": {}, "startTime": 1753034919162}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "build-module-tsx", "id": 80, "timestamp": 484775028, "duration": 252955, "tags": {"name": "C:\\OSPanel\\home\\app.ticketflow.chat\\containers\\addressContainer\\savedAddressList.tsx"}, "startTime": 1753034918965}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 64, "timestamp": 483853783, "duration": 1179501, "tags": {"request": "private-next-pages/reservations/[id].tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 68, "timestamp": 483853792, "duration": 1179513, "tags": {"request": "private-next-pages/shop/index.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 67, "timestamp": 483853790, "duration": 1179522, "tags": {"request": "private-next-pages/shop-category/[id].tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 61, "timestamp": 483853774, "duration": 1180695, "tags": {"request": "private-next-pages/help.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 36, "timestamp": 483853703, "duration": 1180780, "tags": {"request": "private-next-pages/parcels/[id].tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 85, "name": "postcss-process", "id": 86, "timestamp": 485147401, "duration": 26727, "tags": {}, "startTime": 1753034919337}, {"traceId": "cc43856c08056924", "parentId": 81, "name": "postcss-loader", "id": 85, "timestamp": 485147270, "duration": 26903, "tags": {}, "startTime": 1753034919337}, {"traceId": "cc43856c08056924", "parentId": 81, "name": "css-loader", "id": 87, "timestamp": 485174342, "duration": 49152, "tags": {"astUsed": "true"}, "startTime": 1753034919364}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "build-module-scss", "id": 81, "timestamp": 484775214, "duration": 449441, "tags": {"name": "C:\\OSPanel\\home\\app.ticketflow.chat\\containers\\addressContainer\\addressContainer.module.scss"}, "startTime": 1753034918965}, {"traceId": "cc43856c08056924", "parentId": 89, "name": "next-swc-transform", "id": 90, "timestamp": 485230168, "duration": 2269, "tags": {}, "startTime": 1753034919420}, {"traceId": "cc43856c08056924", "parentId": 88, "name": "next-swc-loader", "id": 89, "timestamp": 485230064, "duration": 2381, "tags": {}, "startTime": 1753034919420}, {"traceId": "cc43856c08056924", "parentId": 77, "name": "build-module-tsx", "id": 88, "timestamp": 485229086, "duration": 5466, "tags": {"name": "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressTypeSelector\\addressTypeSelector.tsx"}, "startTime": 1753034919419}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 33, "timestamp": 483853694, "duration": 1382024, "tags": {"request": "private-next-pages/orders/[id].tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 44, "timestamp": 483853734, "duration": 1381995, "tags": {"request": "private-next-pages/reset-password.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 47, "timestamp": 483853741, "duration": 1381991, "tags": {"request": "private-next-pages/update-details.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 48, "timestamp": 483853743, "duration": 1381990, "tags": {"request": "private-next-pages/update-password.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 49, "timestamp": 483853745, "duration": 1381990, "tags": {"request": "private-next-pages/verify-phone.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 25, "timestamp": 483853453, "duration": 1382285, "tags": {"request": "private-next-pages/ads/index.tsx"}, "startTime": 1753034918043}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 39, "timestamp": 483853713, "duration": 1382027, "tags": {"request": "private-next-pages/promotion/index.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 29, "timestamp": 483853495, "duration": 1382247, "tags": {"request": "private-next-pages/careers/index.tsx"}, "startTime": 1753034918043}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 31, "timestamp": 483853683, "duration": 1382062, "tags": {"request": "private-next-pages/login.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 41, "timestamp": 483853727, "duration": 1382020, "tags": {"request": "private-next-pages/referrals.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 42, "timestamp": 483853729, "duration": 1382020, "tags": {"request": "private-next-pages/register.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 30, "timestamp": 483853647, "duration": 1383980, "tags": {"request": "private-next-pages/liked.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 32, "timestamp": 483853690, "duration": 1383943, "tags": {"request": "private-next-pages/order-refunds.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 34, "timestamp": 483853697, "duration": 1383938, "tags": {"request": "private-next-pages/orders/index.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 50, "timestamp": 483853747, "duration": 1383890, "tags": {"request": "private-next-pages/wallet.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 51, "timestamp": 483853750, "duration": 1383890, "tags": {"request": "private-next-pages/ads/[id].tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 37, "timestamp": 483853707, "duration": 1383935, "tags": {"request": "private-next-pages/parcels/index.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 40, "timestamp": 483853716, "duration": 1383929, "tags": {"request": "private-next-pages/recipes/index.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 60, "timestamp": 483853772, "duration": 1383874, "tags": {"request": "private-next-pages/promotion/[id].tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 59, "timestamp": 483853770, "duration": 1383878, "tags": {"request": "private-next-pages/index.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 43, "timestamp": 483853731, "duration": 1383919, "tags": {"request": "private-next-pages/reservations/index.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 63, "timestamp": 483853780, "duration": 1383872, "tags": {"request": "private-next-pages/recipes/[id].tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 66, "timestamp": 483853788, "duration": 1384457, "tags": {"request": "private-next-pages/restaurant/[id]/checkout.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 71, "timestamp": 483853801, "duration": 1395866, "tags": {"request": "private-next-pages/restaurant/[id]/index.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 72, "timestamp": 483853804, "duration": 1395877, "tags": {"request": "private-next-pages/shop/[id].tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 91, "name": "read-resource", "id": 92, "timestamp": 485250679, "duration": 54, "tags": {}, "startTime": 1753034919441}, {"traceId": "cc43856c08056924", "parentId": 93, "name": "postcss-process", "id": 94, "timestamp": 485261070, "duration": 1889, "tags": {}, "startTime": 1753034919451}, {"traceId": "cc43856c08056924", "parentId": 91, "name": "postcss-loader", "id": 93, "timestamp": 485261061, "duration": 1917, "tags": {}, "startTime": 1753034919451}, {"traceId": "cc43856c08056924", "parentId": 91, "name": "css-loader", "id": 95, "timestamp": 485262995, "duration": 2305, "tags": {"astUsed": "true"}, "startTime": 1753034919453}, {"traceId": "cc43856c08056924", "parentId": 88, "name": "build-module-scss", "id": 91, "timestamp": 485250524, "duration": 15270, "tags": {"name": "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressTypeSelector\\addressTypeSelector.module.scss"}, "startTime": 1753034919440}, {"traceId": "cc43856c08056924", "parentId": 80, "name": "build-module-remixicon-react/UserLocationFillIcon", "id": 96, "timestamp": 485265864, "duration": 167, "tags": {"name": "remixicon-react/UserLocationFillIcon"}, "startTime": 1753034919456}, {"traceId": "cc43856c08056924", "parentId": 80, "name": "build-module-remixicon-react/Briefcase2FillIcon", "id": 97, "timestamp": 485266044, "duration": 17, "tags": {"name": "remixicon-react/Briefcase2FillIcon"}, "startTime": 1753034919456}, {"traceId": "cc43856c08056924", "parentId": 80, "name": "build-module-remixicon-react/MapPinFillIcon", "id": 98, "timestamp": 485266119, "duration": 20, "tags": {"name": "remixicon-react/MapPinFillIcon"}, "startTime": 1753034919456}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 21, "timestamp": 483853015, "duration": 1413239, "tags": {"request": "private-next-pages/_app.tsx"}, "startTime": 1753034918043}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "add-entry", "id": 65, "timestamp": 483853785, "duration": 1413121, "tags": {"request": "private-next-pages/saved-locations.tsx"}, "startTime": 1753034918044}, {"traceId": "cc43856c08056924", "parentId": 16, "name": "make", "id": 20, "timestamp": 483852726, "duration": 1414345, "tags": {}, "startTime": 1753034918043}, {"traceId": "cc43856c08056924", "parentId": 99, "name": "get-entries", "id": 100, "timestamp": 485268096, "duration": 279, "tags": {}, "startTime": 1753034919458}, {"traceId": "cc43856c08056924", "parentId": 99, "name": "node-file-trace", "id": 101, "timestamp": 485273152, "duration": 813148, "tags": {"traceEntryCount": "52"}, "startTime": 1753034919463}, {"traceId": "cc43856c08056924", "parentId": 99, "name": "collect-traced-files", "id": 102, "timestamp": 486086313, "duration": 58870, "tags": {}, "startTime": 1753034920276}, {"traceId": "cc43856c08056924", "parentId": 19, "name": "finish-modules", "id": 99, "timestamp": 485267940, "duration": 877257, "tags": {}, "startTime": 1753034919458}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "webpack-compilation-chunk-graph", "id": 104, "timestamp": 486200883, "duration": 30570, "tags": {}, "startTime": 1753034920391}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "webpack-compilation-optimize-modules", "id": 106, "timestamp": 486231568, "duration": 28, "tags": {}, "startTime": 1753034920421}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "webpack-compilation-optimize-chunks", "id": 107, "timestamp": 486231665, "duration": 40640, "tags": {}, "startTime": 1753034920422}, {"traceId": "cc43856c08056924", "parentId": 18, "name": "webpack-compilation-optimize-tree", "id": 108, "timestamp": 486272397, "duration": 120, "tags": {}, "startTime": 1753034920462}]
.address {
  display: flex;
  align-items: center;
  column-gap: 14px;
  cursor: pointer;
  div {
    pointer-events: none;
  }
  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    svg {
      width: 22px;
      height: 22px;
    }
  }
  .addressTitle {
    display: block;
    @media (max-width: 1139px) {
      display: none;
    }
  }
  label {
    letter-spacing: -0.3px;
    font-weight: 500;
    font-size: 12px;
    line-height: 12px;
    color: var(--secondary-text);
  }
  p {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
    margin-top: 5px;

    font-size: 14px;
    line-height: 14px;
    letter-spacing: -0.3px;
    font-weight: 500;
    color: var(--dark-blue);
  }
}

.addressWrapper {
  max-width: 400px;
  max-height: 400px;
  overflow-y: auto;
 
  @media (max-width: 1139px) {
    max-width: 100%;
    padding: 0;
  }

  .list {
    .radioGroup {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      width: 100%;
      padding: 16px 8px;
      border-bottom: 1px solid var(--grey);
      overflow: hidden;
      justify-content: space-between;
      &:hover {

        background-color: var(--grey);
      }

      .radio {
        display: flex;
        align-items: center;
        gap: 8px;

        .addressTypeIcon {
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--primary);
          flex-shrink: 0;
        }

        .text {
          max-width: 290px;
          font-size: 16px;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;

          &.twoLine {
            -webkit-line-clamp: 2; 
          }
        }

        .desc {
          font-size: 14px;
          color: var(--secondary-text);
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .label {
          cursor: pointer;
          flex: 1;
        }
      }

      .edit {
        opacity: 0;
        transition: all 0.2s linear;
      }

      &:hover {
        .edit {
          opacity: 1;
        }
      }
    }
  }

  .add {
    min-width: 300px;
    padding: 16px;
    width: 100%;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 4px;
    &:hover {
      background-color: var(--grey);
    }
  }
}
